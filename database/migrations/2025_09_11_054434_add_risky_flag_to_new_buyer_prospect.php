<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('new_buyer_prospect', function (Blueprint $table) {
            $table->boolean('risky')->default(false)->after('company_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('new_buyer_prospect', function (Blueprint $table) {
            $table->dropColumn('risky');
        });
    }
};
