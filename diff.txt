diff --git a/app/Builder/Billing/CompanyRevenueBuilder.php b/app/Builder/Billing/CompanyRevenueBuilder.php
index e08ed6fcc4..2f35dcad6b 100644
--- a/app/Builder/Billing/CompanyRevenueBuilder.php
+++ b/app/Builder/Billing/CompanyRevenueBuilder.php
@@ -12,6 +12,7 @@
 use App\Models\Legacy\EloquentInvoiceItem;
 use App\Models\Legacy\EloquentQuoteCompany;
 use App\Models\Odin\Company;
+use App\Models\Odin\ConsumerProduct;
 use App\Models\Odin\IndustryService;
 use App\Models\Odin\ProductAssignment;
 use App\Models\Odin\ProductCancellation;
@@ -118,6 +119,11 @@ public function setPeriodDuration(int $duration = 1): self
     protected function getBaseQuery(): Builder
     {
         return ProductAssignment::query()
+            ->join(
+                ConsumerProduct::TABLE,
+                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID,
+                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
+            )
             ->leftJoin(InvoiceItem::TABLE, function ($join) {
                 $join->on(InvoiceItem::TABLE . '.' . InvoiceItem::FIELD_BILLABLE_ID, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID)
                     ->where(InvoiceItem::TABLE . '.' . InvoiceItem::FIELD_BILLABLE_TYPE, ProductAssignment::class);
@@ -142,16 +148,14 @@ protected function getBaseQuery(): Builder
                 DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::INVOICE_ID,
                 DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::INVOICE_ID,
             )
-            ->leftJoin(
-                ProductRejection::TABLE,
-                ProductRejection::TABLE . '.' . ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID,
-                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID
-            )
-            ->leftJoin(
-                ProductCancellation::TABLE,
-                ProductCancellation::TABLE . '.' . ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID,
-                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID
-            )
+            ->leftJoin(ProductRejection::TABLE, function ($join) {
+                $join->on(ProductRejection::TABLE . '.' . ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID)
+                    ->whereNull(ProductRejection::TABLE . '.' . ProductRejection::FIELD_DELETED_AT);
+            })
+            ->leftJoin(ProductCancellation::TABLE, function ($join) {
+                $join->on(ProductCancellation::TABLE . '.' . ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID)
+                    ->whereNull(ProductCancellation::TABLE . '.' . ProductCancellation::FIELD_DELETED_AT);
+            })
             ->leftJoin(
                 Budget::TABLE,
                 Budget::TABLE . '.' . Budget::FIELD_ID,
@@ -304,15 +308,8 @@ protected function getA2PaidSelect(): string
     {
         $invStatus = Invoice::TABLE . '.' . Invoice::FIELD_STATUS;
         $paidStatus = InvoiceStates::PAID->value;
-        $paCost = ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST;
 
-        return "(
-            CASE
-                when $invStatus = '$paidStatus'
-                then $paCost
-                else 0
-            END
-        )";
+        return $this->getProductCostCase("$invStatus = '$paidStatus'");
     }
 
     /**
@@ -323,13 +320,17 @@ protected function getLegacyPaidSelect(): string
         $legacyInvoiceStatus = DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS;
         $paCost = ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST;
 
-        return "(
-            CASE
-                when $legacyInvoiceStatus = 'paid'
-                then $paCost
-                else 0
-            END
-        )";
+        return $this->getProductCostCase("$legacyInvoiceStatus = 'paid'");
+    }
+
+    protected function getProductCostCase(string $condition, string $then = ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST): string
+    {
+        return "
+            (CASE
+			    when MAX(CASE WHEN $condition THEN 1 ELSE 0 END) = 1
+                    then $then
+			    else 0
+		    END)";
     }
 
     /**
@@ -338,62 +339,66 @@ protected function getLegacyPaidSelect(): string
     public function getSummary(): array
     {
         $pa = ProductAssignment::TABLE;
+        $pr = ProductRejection::TABLE;
+        $pc = ProductCancellation::TABLE;
+        $cp = ConsumerProduct::TABLE;
         $i = Invoice::TABLE;
 
         $thirtyDaysAgo = Carbon::today()->subDays(29)->toIso8601String();
         $sixMonthsAgo = Carbon::today()->subMonths(6)->toIso8601String();
         $startOfYear = Carbon::today()->startOfYear()->toIso8601String();
+        $legacyInvoiceStatus = DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS;
 
-
-        $chargeableDeliveredLast30Days = "
-            (
-                CASE
-                    when $pa.delivered_at >= '$thirtyDaysAgo'
-	                then " . ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST . "
-                    else 0
-                END
-            )";
-
-        $chargeableDeliveredLast6Months = "
-            (
-                CASE
-                    when $i.status = 'paid' and $pa.delivered_at >= '$sixMonthsAgo'
-	                then " . ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST . "
-                    else 0
-                END
-            )
-            ";
-
-        $outstandingChargeableDeliveredAllTime = "
-            (
-                CASE
-                    when ($i.id is null or $i.status <> 'paid') and $pa.chargeable = true and $pa.delivered = true
-	                then " . ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST . "
-                    else 0
-                END
-            )";
-
-        $paidYearToDate = "
-            (
-                CASE
-                    when $i.status = 'paid' and $pa.delivered_at >= '$startOfYear'
-	                then " . ProductAssignment::TABLE . "." . ProductAssignment::FIELD_COST . "
-                    else 0
-                END
-            )";
+        $billableCondition = "($pa.chargeable = true and $pa.delivered = true and $pr.id is null and $pc.id is null)";
+        $paidCondition = "(invoices.status = 'paid' or $legacyInvoiceStatus = 'paid')";
+
+        $billableLast30Days = $this->getProductCostCase("$pa.delivered_at >= '$thirtyDaysAgo' and $billableCondition");
+        $billableLast6Months = $this->getProductCostCase("$pa.delivered_at >= '$sixMonthsAgo' and $billableCondition");
+        $billableYearToDate = $this->getProductCostCase("$pa.delivered_at >= '$startOfYear' and $billableCondition");
+
+        $paidAllTime = $this->getProductCostCase($paidCondition);
+        $paidLast30Days = $this->getProductCostCase("$pa.delivered_at >= '$thirtyDaysAgo' and $paidCondition");
+        $paidLast6Months = $this->getProductCostCase("$pa.delivered_at >= '$sixMonthsAgo' and $paidCondition");
+        $paidYearToDate = $this->getProductCostCase("$pa.delivered_at >= '$startOfYear' and $paidCondition");
+        $lastDeliveryAt = $this->getProductCostCase("$pa.delivered = true", "$pa.delivered_at");
+        $a2Paid = $this->getA2PaidSelect();
+        $legacyPaid = $this->getLegacyPaidSelect();
+
+        $legacyQuoteCompany = DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE;
+
+        $toInvoice = $this->getProductCostCase(
+            "$pa.delivered = true
+            and $pa.chargeable = true
+            and $pr.id is null
+            and $pc.id is null
+            and ($pa.legacy_id is null or $legacyQuoteCompany.billing_version = 'v2')
+            and $i.id is null
+            and $pa.rejection_expiry < current_time()
+            and $cp.status = 3
+            "
+        );
 
         $subquery = $this->getBaseQuery()
             ->select([
-                DB::raw($this->getA2PaidSelect() . " as a2_all_time_paid"),
-                DB::raw($this->getLegacyPaidSelect() . " as legacy_all_time_paid"),
-                DB::raw("$chargeableDeliveredLast30Days as paid_last_30_days"),
-                DB::raw("$chargeableDeliveredLast6Months as paid_last_6_months"),
-                DB::raw($this->getChargeableDeliveredSelect() . " as chargeable_delivered_all_time"),
+                DB::raw("$a2Paid as a2_paid"),
+                DB::raw("$legacyPaid as legacy_paid"),
+
+                DB::raw("$paidLast30Days as paid_last_30_days"),
+                DB::raw("$paidLast6Months as paid_last_6_months"),
                 DB::raw("$paidYearToDate as paid_year_to_date"),
-                DB::raw("$outstandingChargeableDeliveredAllTime as outstanding_chargeable_delivered_all_time"),
+
+                DB::raw($this->getChargeableDeliveredSelect() . " as billable_all_time"),
+                DB::raw("$billableLast30Days as billable_last_30_days"),
+                DB::raw("$billableLast6Months as billable_last_6_months"),
+                DB::raw("$billableYearToDate as billable_year_to_date"),
+                DB::raw("$lastDeliveryAt as last_delivered_at"),
+
+                DB::raw("$toInvoice as to_invoice"),
+
                 DB::raw($this->getRejectedSelect() . " as rejected_all_time"),
                 DB::raw($this->getCancelledSelect() . " as cancelled_all_time"),
                 ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID,
+                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID,
                 Company::TABLE . '.' . Company::FIELD_NAME,
             ])
             ->when(filled($this->companyId), function ($query) {
@@ -404,41 +409,85 @@ public function getSummary(): array
                 Company::TABLE . '.' . Company::FIELD_ID,
                 ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID,
             )
-            ->groupBy(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID);
+            ->groupBy(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID);
 
         $summary = DB::query()
             ->select([
-                DB::raw('SUM(sub.a2_all_time_paid) AS a2_all_time_paid'),
-                DB::raw('SUM(sub.legacy_all_time_paid) AS legacy_all_time_paid'),
-                DB::raw('SUM(sub.a2_all_time_paid) + SUM(sub.legacy_all_time_paid)  AS paid_all_time'),
-                DB::raw('SUM(sub.paid_last_30_days) AS paid_last_30_days'),
-                DB::raw('SUM(sub.paid_last_6_months) AS paid_last_6_months'),
-                DB::raw('SUM(sub.paid_year_to_date) AS paid_year_to_date'),
-                DB::raw('SUM(sub.chargeable_delivered_all_time) AS chargeable_delivered_all_time'),
-                DB::raw('SUM(sub.outstanding_chargeable_delivered_all_time) AS outstanding_chargeable_delivered_all_time'),
-                DB::raw('SUM(sub.rejected_all_time) AS rejected_all_time'),
-                DB::raw('SUM(sub.cancelled_all_time) AS cancelled_all_time'),
+                DB::raw('SUM(sub.a2_paid) AS a2_all_time_paid'),
+                DB::raw('SUM(sub.legacy_paid) AS legacy_all_time_paid'),
+
+                DB::raw('SUM(sub.a2_paid) + SUM(sub.legacy_paid) AS total_paid_all_time'),
+                DB::raw('COUNT(DISTINCT CASE WHEN sub.a2_paid > 0 or sub.legacy_paid > 0 then sub.id else null end) AS count_paid_all_time'),
+
+                DB::raw('SUM(sub.paid_last_30_days) AS total_paid_last_30_days'),
+                DB::raw('COUNT(DISTINCT CASE WHEN sub.paid_last_30_days > 0 then sub.id else null end) AS count_paid_last_30_days'),
+
+                DB::raw('SUM(sub.paid_last_6_months) AS total_paid_last_6_months'),
+                DB::raw('COUNT(DISTINCT CASE WHEN sub.paid_last_6_months > 0 then sub.id else null end) AS count_paid_last_6_months'),
+
+                DB::raw('SUM(sub.paid_year_to_date) AS total_paid_year_to_date'),
+                DB::raw('COUNT(DISTINCT CASE WHEN sub.paid_year_to_date > 0 then sub.id else null end) AS count_paid_year_to_date'),
+
+                DB::raw('SUM(sub.billable_all_time) AS total_billable_all_time'),
+                DB::raw('COUNT(DISTINCT CASE WHEN sub.billable_all_time > 0 then sub.id else null end) AS count_billable_all_time'),
+
+                DB::raw('COUNT(DISTINCT CASE WHEN sub.billable_last_30_days > 0 then sub.id else null end) AS count_billable_last_30_days'),
+                DB::raw('SUM(sub.billable_last_30_days) AS total_billable_last_30_days'),
+
+                DB::raw('COUNT(DISTINCT CASE WHEN sub.billable_last_6_months > 0 then sub.id else null end) AS count_billable_last_6_months'),
+                DB::raw('SUM(sub.billable_last_6_months) AS total_billable_last_6_months'),
+
+                DB::raw('COUNT(DISTINCT CASE WHEN sub.billable_year_to_date > 0 then sub.id else null end) AS count_billable_year_to_date'),
+                DB::raw('SUM(sub.billable_year_to_date) AS total_billable_year_to_date'),
+
+                DB::raw('SUM(sub.billable_last_30_days) / 29 AS average_daily_spend_last_30_days'),
+
+                DB::raw('SUM(sub.rejected_all_time) AS total_rejected_all_time'),
+                DB::raw('COUNT(DISTINCT CASE WHEN sub.rejected_all_time > 0 then sub.id else null end) AS count_rejected_all_time'),
+
+                DB::raw('SUM(sub.cancelled_all_time) AS total_cancelled_all_time'),
+                DB::raw('COUNT(DISTINCT CASE WHEN sub.cancelled_all_time > 0 then sub.id else null end) AS count_cancelled_all_time'),
+
+                DB::raw('SUM(sub.to_invoice) AS total_to_invoice'),
+
+                DB::raw('MAX(sub.last_delivered_at) AS last_delivered_at'),
             ])
             ->fromSub($subquery, 'sub')
             ->groupBy('sub.company_id')
             ->first();
 
         return [
-            'last_30_days_start_date'       => Carbon::parse($thirtyDaysAgo)->format('M j, Y h:iA e'),
-            'last_6_months_start_date'      => Carbon::parse($sixMonthsAgo)->format('M j, Y h:iA e'),
-            'start_of_year_start_date'      => Carbon::parse($startOfYear)->format('M j, Y h:iA e'),
-            'paid_all_time'                 => $summary?->paid_all_time ?? 0,
-            'last_30_days'                  => $summary?->paid_last_30_days ?? 0,
-            'last_6_months'                 => $summary?->paid_last_6_months ?? 0,
-            'year_to_date'                  => $summary?->paid_year_to_date ?? 0,
-            'chargeable_delivered_all_time' => $summary?->chargeable_delivered_all_time ?? 0,
-            'a2_all_time_paid'              => $summary?->a2_all_time_paid ?? 0,
-            'legacy_all_time_paid'          => $summary?->legacy_all_time_paid ?? 0,
-            'paid_last_30_days'             => $summary?->paid_last_30_days ?? 0,
-            'paid_last_6_months'            => $summary?->paid_last_6_months ?? 0,
-            'paid_year_to_date'             => $summary?->paid_year_to_date ?? 0,
-            'rejected_all_time'             => $summary?->rejected_all_time ?? 0,
-            'cancelled_all_time'            => $summary?->cancelled_all_time ?? 0,
+            'last_30_days_start_date'  => Carbon::parse($thirtyDaysAgo)->format('M j, Y h:iA e'),
+            'last_6_months_start_date' => Carbon::parse($sixMonthsAgo)->format('M j, Y h:iA e'),
+            'start_of_year_start_date' => Carbon::parse($startOfYear)->format('M j, Y h:iA e'),
+
+            'a2_all_time_paid'                 => $summary?->a2_all_time_paid,
+            'legacy_all_time_paid'             => $summary?->legacy_all_time_paid ?? 0,
+            'total_paid_all_time'              => $summary?->total_paid_all_time ?? 0,
+            'count_paid_all_time'              => $summary?->count_paid_all_time ?? 0,
+            'total_paid_last_30_days'          => $summary?->total_paid_last_30_days ?? 0,
+            'count_paid_last_30_days'          => $summary?->count_paid_last_30_days ?? 0,
+            'total_paid_last_6_months'         => $summary?->total_paid_last_6_months ?? 0,
+            'count_paid_last_6_months'         => $summary?->count_paid_last_6_months ?? 0,
+            'total_paid_year_to_date'          => $summary?->total_paid_year_to_date ?? 0,
+            'count_paid_year_to_date'          => $summary?->count_paid_year_to_date ?? 0,
+            'total_billable_all_time'          => $summary?->total_billable_all_time ?? 0,
+            'count_billable_all_time'          => $summary?->count_billable_all_time ?? 0,
+            'count_billable_last_30_days'      => $summary?->count_billable_last_30_days ?? 0,
+            'total_billable_last_30_days'      => $summary?->total_billable_last_30_days ?? 0,
+            'count_billable_last_6_months'     => $summary?->count_billable_last_6_months ?? 0,
+            'total_billable_last_6_months'     => $summary?->total_billable_last_6_months ?? 0,
+            'count_billable_year_to_date'      => $summary?->count_billable_year_to_date ?? 0,
+            'total_billable_year_to_date'      => $summary?->total_billable_year_to_date ?? 0,
+            'average_daily_spend_last_30_days' => round($summary?->average_daily_spend_last_30_days ?? 0, 2),
+            'total_rejected_all_time'          => $summary?->total_rejected_all_time ?? 0,
+            'count_rejected_all_time'          => $summary?->count_rejected_all_time ?? 0,
+            'total_cancelled_all_time'         => $summary?->total_cancelled_all_time ?? 0,
+            'count_cancelled_all_time'         => $summary?->count_cancelled_all_time ?? 0,
+            'last_delivered_at'                => $summary?->last_delivered_at ?? 0,
+            'total_to_invoice'                 => $summary?->total_to_invoice ?? 0,
+
+            'requested_at' => now()->toIso8601String()
         ];
     }
 
diff --git a/app/Builders/ActivityBuilder.php b/app/Builders/ActivityBuilder.php
index 0543844d78..4332700ab4 100644
--- a/app/Builders/ActivityBuilder.php
+++ b/app/Builders/ActivityBuilder.php
@@ -40,10 +40,7 @@ public function __construct(
         protected ?string       $sortByColumn = null,
         protected ?string       $sortDirection = 'desc',
         protected bool          $includeCadenceActivity = true,
-        protected ?array        $relations = [],
-    )
-    {
-    }
+    ) {}
 
     /**
      * Handles applying company filter for querying activities.
@@ -132,7 +129,7 @@ public function setSearchQuery(?string $query = null): self
      */
     public function sortBy(?string $column = null, ?string $direction = 'desc'): self
     {
-        $this->sortByColumn = $column;
+        $this->sortByColumn  = $column;
         $this->sortDirection = $direction;
 
         return $this;
@@ -148,12 +145,6 @@ public function includeCadenceActivity(bool $includeCadenceActivity): self
         return $this;
     }
 
-    public function withRelations(?array $relations = []): self
-    {
-        $this->relations = $relations;
-        return $this;
-    }
-
     /**
      * Runs the query builder, and returns a list of tasks.
      *
@@ -162,7 +153,7 @@ public function withRelations(?array $relations = []): self
     public function get(): Collection
     {
         return $this->getQuery()
-            ->get();
+                    ->get();
     }
 
     /**
@@ -214,8 +205,6 @@ public function getQuery(): Builder
         if (!$this->includeCadenceActivity)
             $query->whereNull(ActivityFeed::FIELD_COMPANY_CADENCE_GROUP_ACTION_ID);
 
-        $query->when($this->relations, fn($query) => $query->with($this->relations));
-
         $query->whereNull(ActivityFeed::FIELD_DELETED_AT);
 
         return $query;
@@ -288,21 +277,21 @@ protected function appendSearchQueryToBuilder(string $searchQuery, Builder $quer
     protected function appendMorphRelationshipData(Builder $query, ?ActivityType $type): void
     {
         $itemTypeRelationships = [
-            ActivityType::ACTION->value        =>
+            ActivityType::ACTION->value =>
                 [Action::class => ['category:id,name']],
-            ActivityType::TEXT->value          =>
+            ActivityType::TEXT->value   =>
                 [Text::class => ['phone:id,phone']],
-            ActivityType::EMAIL->value         =>
+            ActivityType::EMAIL->value  =>
                 [Email::class => []], // todo
-            ActivityType::MAILBOX_EMAIL->value =>
+            ActivityType::MAILBOX_EMAIL->value  =>
                 [MailboxEmail::class => [
                     MailboxEmail::RELATION_RECIPIENTS . '.' . MailboxEmailRecipient::RELATION_IDENTIFIED_CONTACT,
                     MailboxEmail::RELATION_FROM_IDENTIFIED_CONTACT,
                     MailboxEmail::RELATION_ATTACHMENTS
                 ]], // todo
-            ActivityType::CALL->value          =>
+            ActivityType::CALL->value   =>
                 [Call::class => ['callRecording:id,recording_link,call_id']],
-            ActivityType::TASK->value          =>
+            ActivityType::TASK->value   =>
                 [Task::class => ['assignedUser:id,name,email', 'taskCategory:id,name', 'taskType:id,name', 'taskNotes' => fn(
                     HasMany $query
                 ) => $query->with(['user:id,name'])]],
@@ -325,7 +314,7 @@ protected function appendMorphRelationshipData(Builder $query, ?ActivityType $ty
     public function count(): int
     {
         return $this->getQuery()
-            ->count();
+                    ->count();
     }
 
     /**
@@ -345,6 +334,6 @@ public function paginate(
     ): LengthAwarePaginator
     {
         return $this->getQuery()
-            ->paginate($items, $columns, $pageName, $page);
+                    ->paginate($items, $columns, $pageName, $page);
     }
 }
diff --git a/app/Http/Controllers/API/CompaniesController.php b/app/Http/Controllers/API/CompaniesController.php
index 27ed28414c..dde739fb43 100644
--- a/app/Http/Controllers/API/CompaniesController.php
+++ b/app/Http/Controllers/API/CompaniesController.php
@@ -36,6 +36,7 @@
 use App\Http\Requests\UpdateChargeableStatusRequest;
 use App\Http\Requests\UpdateCompanySalesStatusRequest;
 use App\Http\Resources\ActionResource;
+use App\Http\Resources\Billing\CompanyRevenueGraphSummaryResource;
 use App\Http\Resources\Companies\Sales\AccountManagerResource;
 use App\Http\Resources\Odin\CompanyCampaignListOptionResource;
 use App\Http\Resources\Odin\CompanyContractsResource;
@@ -585,28 +586,19 @@ public function createCompanyLink(int $companyId, CreateCompanyLinkRequest $requ
     }
 
     /**
-     * @param int $companyId
-     * @param RevenueRepository $revenueRepository ,
-     * @param CompanyRepository $companyRepository
-     *
-     * @return JsonResponse
+     * @param Company $companyId
+     * @return CompanyRevenueGraphSummaryResource
      */
     public function getRevenueOverview(
-        int               $companyId,
-        RevenueRepository $revenueRepository,
-        CompanyRepository $companyRepository
-    ): JsonResponse
+        Company $companyId,
+    ): CompanyRevenueGraphSummaryResource
     {
-        $company = $companyRepository->findOrFail($companyId);
+        $summary = Cache::remember("revenue-overview-$companyId", now()->addHour(), function () use ($companyId) {
+            $companyRevenueService = new \App\Builder\Billing\CompanyRevenueBuilder($companyId->id);
+            return $companyRevenueService->getSummary();
+        });
 
-        $isV2 = $this->companyBillingRepository->isV2($company);
-
-        return $this->formatResponse([
-            'v1' => $revenueRepository->getRevenueOverview($company),
-            ...(
-            $isV2 ? ['v2' => new CompanyInvoiceSummaryResource($this->invoiceService->getCompanyInvoiceSummary($company))] : []
-            ),
-        ]);
+        return new CompanyRevenueGraphSummaryResource($summary);
     }
 
     /**
diff --git a/app/Http/Controllers/API/CompanyActivityController.php b/app/Http/Controllers/API/CompanyActivityController.php
index 7301cbf10a..fac2f9ebe0 100644
--- a/app/Http/Controllers/API/CompanyActivityController.php
+++ b/app/Http/Controllers/API/CompanyActivityController.php
@@ -88,7 +88,6 @@ public function getActivities(GetCompanyActivitiesRequest $request, ActivityTran
                 : null,
             sortOrder: $filters->get(self::REQUEST_SORT_BY),
             cadence: $filters->get(self::REQUEST_CADENCE),
-            relations: [ActivityFeed::RELATION_USER]
         );
 
         $paginatedActivities =
diff --git a/app/Http/Controllers/Billing/CompanyRevenueController.php b/app/Http/Controllers/Billing/CompanyRevenueController.php
index 86e5dc79e5..8bb4bdf815 100644
--- a/app/Http/Controllers/Billing/CompanyRevenueController.php
+++ b/app/Http/Controllers/Billing/CompanyRevenueController.php
@@ -57,18 +57,4 @@ function getRevenueGraphData(int $company, GetCompanyRevenueGraphDataRequest $re
             'industry_options' => $industryOptions
         ]);
     }
-
-    /**
-     * @param int $company
-     * @param GetCompanyRevenueGraphDataRequest $request
-     * @return array
-     */
-    function getRevenueSummary(int $company, GetCompanyRevenueGraphDataRequest $request): CompanyRevenueGraphSummaryResource
-    {
-        $validated = $request->validated();
-
-        $companyRevenueService = new CompanyRevenueBuilder($company);
-
-        return new CompanyRevenueGraphSummaryResource($companyRevenueService->getSummary());
-    }
 }
diff --git a/app/Http/Requests/CompanyCompanyRelationship/CreateCompanyCompanyRelationshipRequest.php b/app/Http/Requests/CompanyCompanyRelationship/CreateCompanyCompanyRelationshipRequest.php
new file mode 100644
index 0000000000..3c618e9a92
--- /dev/null
+++ b/app/Http/Requests/CompanyCompanyRelationship/CreateCompanyCompanyRelationshipRequest.php
@@ -0,0 +1,42 @@
+<?php
+
+namespace App\Http\Requests\CompanyCompanyRelationship;
+
+use App\Enums\PermissionType;
+use App\Models\Odin\Company;
+use App\Models\User;
+use Illuminate\Foundation\Http\FormRequest;
+use Illuminate\Support\Facades\Auth;
+use Illuminate\Validation\Rule;
+
+class CreateCompanyCompanyRelationshipRequest extends FormRequest
+{
+    const string COMPANY_ID        = 'company_id';
+    const string TARGET_COMPANY_ID = 'target_company_id';
+    const string RELATIONSHIP      = 'relationship';
+
+    /**
+     * Determine if the user is authorized to make this request.
+     *
+     * @return bool
+     */
+    public function authorize(): bool
+    {
+        /** @var User $user */
+        $user = Auth::user();
+
+        return $user->hasPermissionTo(PermissionType::COMPANY_USER_RELATIONSHIPS_EDIT->value);
+    }
+
+    /**
+     * @return array
+     */
+    public function rules(): array
+    {
+        return [
+            self::COMPANY_ID        => ['required', 'integer', Rule::exists(Company::class, 'id')],
+            self::TARGET_COMPANY_ID => ['required', 'integer', Rule::exists(Company::class, 'id'), 'different:' . self::COMPANY_ID],
+            self::RELATIONSHIP      => ['required', 'string', 'max:255'],
+        ];
+    }
+}
diff --git a/app/Jobs/Billing/CheckAndAlertForOverdueInvoicesJob.php b/app/Jobs/Billing/CheckAndAlertForOverdueInvoicesJob.php
index 543d2054d1..7408aee100 100644
--- a/app/Jobs/Billing/CheckAndAlertForOverdueInvoicesJob.php
+++ b/app/Jobs/Billing/CheckAndAlertForOverdueInvoicesJob.php
@@ -101,7 +101,7 @@ protected function dispatchEvents(Collection $dueUnpaidInvoices): void
      */
     protected function getDueUnpaidInvoices(): Collection
     {
-        $graceDate = now()->copy()->subDays(self::DAYS_UNTIL_SUSPENSION);
+        $graceDate = now()->copy()->subWeekdays(self::DAYS_UNTIL_SUSPENSION);
         return Invoice::unpaid()
             ->overdue($graceDate)
             ->whereDoesntHave(Invoice::RELATION_INVOICE_ITEMS, function ($query) {
diff --git a/app/Jobs/Companies/ReleaseCompaniesBackToQueue.php b/app/Jobs/Companies/ReleaseCompaniesBackToQueue.php
index 11857dfc99..067ab16720 100644
--- a/app/Jobs/Companies/ReleaseCompaniesBackToQueue.php
+++ b/app/Jobs/Companies/ReleaseCompaniesBackToQueue.php
@@ -4,10 +4,10 @@
 
 use App\Enums\ActivityType;
 use App\Models\ActivityFeed;
+use App\Models\CompanyUserRelationship;
 use App\Models\Odin\Company;
 use App\Models\Odin\ProductAssignment;
 use App\Models\Sales\Task;
-use Illuminate\Contracts\Database\Eloquent\Builder;
 use Illuminate\Contracts\Queue\ShouldQueue;
 use Illuminate\Foundation\Queue\Queueable;
 
@@ -17,13 +17,14 @@ class ReleaseCompaniesBackToQueue implements ShouldQueue
 
     public function handle(): void
     {
-        $olderThan5Days = fn ($query) => $query->whereDate('company_user_relationships.created_at', '<=', now()->subDays(5));
-
         Company::query()
             ->doesntHave(Company::RELATION_ACCOUNT_MANAGER)
-            ->where(function (Builder $query) use ($olderThan5Days) {
-                $query->whereHas(Company::RELATION_BUSINESS_DEVELOPMENT_MANAGER, $olderThan5Days)
-                    ->orWhereHas(Company::RELATION_SALES_DEVELOPMENT_REPRESENTATIVE, $olderThan5Days);
+            ->whereHas(Company::RELATION_BUSINESS_DEVELOPMENT_MANAGER, function ($query) {
+                $query->whereDate(
+                    CompanyUserRelationship::TABLE.'.'.CompanyUserRelationship::FIELD_CREATED_AT,
+                    '<=',
+                    now()->subDays(5)
+                );
             })->whereDoesntHave(Company::RELATION_ACTIVITY_FEEDS, function ($query) {
                 $query->whereIn(ActivityFeed::FIELD_ITEM_TYPE, [
                     ActivityType::CALL->value,
@@ -32,7 +33,7 @@ public function handle(): void
                 ])->whereDate(ActivityFeed::FIELD_CREATED_AT, '>=', now()->subDays(5));
             })->whereDoesntHave(Company::RELATION_TASKS, function ($query) {
                 $query->whereDate(Task::FIELD_AVAILABLE_AT, '>=', now()->subDays(5));
-            })->whereDoesntHave(Company::RELATION_PRODUCT_ASSIGNMENTS, function ($query) {
+            })->whereDoesntHave(Company::RELATION_PRODUCT_ASSIGNMENTS, function($query) {
                 $query->whereDate(ProductAssignment::FIELD_CREATED_AT, '>=', now()->subDays(120));
             })->each(function ($company) {
                 $company->unassignBusinessDevelopmentManager();
diff --git a/app/Models/Odin/Company.php b/app/Models/Odin/Company.php
index e446f490e9..885aecb8fd 100644
--- a/app/Models/Odin/Company.php
+++ b/app/Models/Odin/Company.php
@@ -265,7 +265,6 @@ class Company extends BaseModel
     const RELATION_TASKS                            = 'tasks';
     const RELATION_ACCOUNT_MANAGER                  = 'accountManager';
     const RELATION_BUSINESS_DEVELOPMENT_MANAGER     = 'businessDevelopmentManager';
-    const RELATION_SALES_DEVELOPMENT_REPRESENTATIVE = 'salesDevelopmentRepresentative';
     const RELATION_COMPANY_RELATIONSHIPS            = 'companyRelationships';
     const RELATION_TARGET_COMPANY_RELATIONSHIPS     = 'targetCompanyRelationships';
 
diff --git a/app/Repositories/ActivityFeedRepository.php b/app/Repositories/ActivityFeedRepository.php
index 570116f419..e6292f5581 100644
--- a/app/Repositories/ActivityFeedRepository.php
+++ b/app/Repositories/ActivityFeedRepository.php
@@ -25,7 +25,6 @@ public function getActivityFeeds(
         ?string       $sortByColumn = null,
         ?string       $sortOrder = null,
         bool          $cadence = true,
-        ?array        $relations = [],
     ): ?Builder
     {
         /** @var ActivityBuilder|Builder $query */
@@ -38,7 +37,6 @@ public function getActivityFeeds(
             ->setPeriodEndingDate($endDate)
             ->sortBy($sortByColumn, $sortOrder)
             ->includeCadenceActivity($cadence)
-            ->withRelations($relations)
             ->getQuery();
         return $query;
     }
@@ -119,12 +117,12 @@ public function findActivityByItemIdAndType(
      * @return ?ActivityFeed
      */
     public function createActivity(
-        int          $itemId,
+        int $itemId,
         ActivityType $itemType,
-        int          $companyId,
-        int          $userId,
-        ?int         $companyCadenceActionId = null,
-        ?Carbon      $createdAt = null,
+        int $companyId,
+        int $userId,
+        ?int $companyCadenceActionId = null,
+        ?Carbon $createdAt = null,
     ): ?ActivityFeed
     {
         /** @var ActivityFeed|null */
@@ -148,12 +146,12 @@ public function createActivity(
      * @return ActivityFeed|null
      */
     public function firstOrCreateByItemAndCompany(
-        int          $itemId,
+        int $itemId,
         ActivityType $itemType,
-        int          $companyId,
-        int          $userId,
-        ?int         $companyCadenceActionId = null,
-        ?Carbon      $createdAt = null
+        int $companyId,
+        int $userId,
+        ?int $companyCadenceActionId = null,
+        ?Carbon $createdAt = null
     ): ?ActivityFeed
     {
         $activity = ActivityFeed::query()
@@ -163,12 +161,12 @@ public function firstOrCreateByItemAndCompany(
             ->first();
 
         return $activity ?? $this->createActivity(
-            itemId: $itemId,
-            itemType: $itemType,
-            companyId: $companyId,
-            userId: $userId,
+            itemId                : $itemId,
+            itemType              : $itemType,
+            companyId             : $companyId,
+            userId                : $userId,
             companyCadenceActionId: $companyCadenceActionId,
-            createdAt: $createdAt
+            createdAt             : $createdAt
         );
     }
 
diff --git a/resources/js/vue/components/Billing/CreateInvoiceModalContentItemList.vue b/resources/js/vue/components/Billing/CreateInvoiceModalContentItemList.vue
index bd04975ac7..9cef7c50a0 100644
--- a/resources/js/vue/components/Billing/CreateInvoiceModalContentItemList.vue
+++ b/resources/js/vue/components/Billing/CreateInvoiceModalContentItemList.vue
@@ -20,28 +20,27 @@
                 :style="showEvents ? 'transition duration-150 rotate-180' : 'transition duration-150'"
             />
         </div>
-        <div v-if="showEvents"
-            class="grid pb-3 mt-4 font-bold text-xs text-slate-500 uppercase gap-6 pr-6"
-            :class="{'grid-cols-9': actionable, 'grid-cols-8': !actionable,  'border-dark-border' : darkMode, 'border-light-border' : !darkMode}"
-        >
-            <div class="col-span-4">
-                Description
-            </div>
-            <div>
-                Quantity
-            </div>
-            <div>
-                Price
-            </div>
-            <div>
-                Added By
-            </div>
-            <div :class="{'flex justify-end': !actionable}">
-                Amount
+        <div v-if="showEvents" class="mt-4 overflow-y-auto max-h-64 pr-6">
+            <div
+                class="grid pb-3  font-bold text-xs text-slate-500 uppercase gap-6"
+                :class="{'grid-cols-9': actionable, 'grid-cols-8': !actionable,  'border-dark-border' : darkMode, 'border-light-border' : !darkMode}"
+            >
+                <div class="col-span-4">
+                    Description
+                </div>
+                <div>
+                    Quantity
+                </div>
+                <div>
+                    Item Price
+                </div>
+                <div>
+                    Added By
+                </div>
+                <div :class="{'flex justify-end': !actionable}">
+                    Amount
+                </div>
             </div>
-        </div>
-
-        <div v-if="showEvents" class="pr-6 relative z-10" :class="[!invoiceStore.isEditable ? 'overflow-y-auto min-h-32 max-h-64' : '']">
             <div v-if="invoiceStore.invoiceItems.length === 0"
                  class="flex justify-center items-center py-2 text-xs font-semibold text-slate-500">
                 No Items Added
@@ -64,14 +63,10 @@
                     </template>
                 </invoice-item>
             </div>
-        </div>
-        <div v-if="showEvents">
-            <div v-if="invoiceStore.isEditable" class="grid gap-6 mt-4 items-center">
-                <div class="flex gap-2">
-                    <button-dropdown
-                        class="relative z-10"
-                        :dark-mode="darkMode" @selected="(option) => invoiceStore.invoiceItemAdded(option.id)"
-                        :options="invoiceItemTypes" options-list-placement="left" position="bottom-0">
+            <div class="grid gap-6 mt-4 items-center">
+                <div v-show="invoiceStore.isEditable" class="flex gap-2">
+                    <button-dropdown :dark-mode="darkMode" @selected="(option) => invoiceStore.invoiceItemAdded(option.id)"
+                                     :options="invoiceItemTypes" options-list-placement="left">
                         <custom-button :dark-mode="darkMode">+ Add an item</custom-button>
                     </button-dropdown>
                     <custom-button
diff --git a/resources/js/vue/components/Billing/ViewCreateInvoiceModalContent.vue b/resources/js/vue/components/Billing/ViewCreateInvoiceModalContent.vue
index 9171aa8b2e..31bd4de2e7 100644
--- a/resources/js/vue/components/Billing/ViewCreateInvoiceModalContent.vue
+++ b/resources/js/vue/components/Billing/ViewCreateInvoiceModalContent.vue
@@ -95,7 +95,7 @@
             </div>
             <div
                 v-if="(!readonly && !invoiceStore.hasPendingAction && !invoiceStore.locked) || invoiceStore?.status?.id === 'failed'"
-                class="flex p-5 gap-4 bottom-0 sticky rounded-b-lg border-t z-30"
+                class="flex p-5 gap-4 bottom-0 sticky rounded-b-lg border-t"
                 :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}"
             >
                 <custom-button @click="createUpdateInvoice(action.id)" v-for="action in invoiceStore.status.actions" :dark-mode="darkMode">{{action.title}}</custom-button>
diff --git a/resources/js/vue/components/Billing/components/CreateNewInvoice/InvoiceItem.vue b/resources/js/vue/components/Billing/components/CreateNewInvoice/InvoiceItem.vue
index df66989501..38a555ca08 100644
--- a/resources/js/vue/components/Billing/components/CreateNewInvoice/InvoiceItem.vue
+++ b/resources/js/vue/components/Billing/components/CreateNewInvoice/InvoiceItem.vue
@@ -7,7 +7,7 @@
         <textarea
             v-if="modelValue.billable_type === 'manual'"
             :disabled="disabled"
-            class="flex col-span-4 px-0 border-none focus:ring-0 resize-none bg-transparent"
+            class="flex col-span-4  border-none focus:ring-0 resize-none bg-transparent"
             :class="{'': !darkMode, 'bg-dark-module text-blue-400': darkMode}"
             placeholder="Item description..."
             v-model="modelValue.description"
@@ -27,7 +27,7 @@
             {{modelValue.description}}
         </p>
         <!-- option type of credit -->
-        <div v-else class="flex col-span-4 border-none relative focus:ring-0 resize-none">
+        <div v-else class="flex col-span-4  border-none relative focus:ring-0 resize-none">
             <dropdown
                 :disabled="disabled"
                 class="z-100 flex w-fit"
@@ -35,7 +35,6 @@
                 :dark-mode="darkMode"
                 v-model="modelValue.billable_id"
                 placeholder="Select Credit Type"
-                placement="top"
             />
         </div>
         <custom-inline-input
@@ -66,11 +65,10 @@ import CustomInlineInput from "../../../Shared/components/CustomInlineInput.vue"
 import Dropdown from "../../../Shared/components/Dropdown.vue";
 import {useCreditManagementStore} from "../../../../../stores/credit/credit-management.store";
 import {useUserStore} from "../../../../../stores/user-store";
-import CustomInput from "../../../Shared/components/CustomInput.vue";
 
 export default {
     name: "InvoiceItem",
-    components: {CustomInput, Dropdown, CustomInlineInput},
+    components: {Dropdown, CustomInlineInput},
     props: {
         darkMode: {
             type: Boolean,
diff --git a/resources/js/vue/components/Billing/services/company-revenue.js b/resources/js/vue/components/Billing/services/company-revenue.js
index 91efa3b445..f9184035cd 100644
--- a/resources/js/vue/components/Billing/services/company-revenue.js
+++ b/resources/js/vue/components/Billing/services/company-revenue.js
@@ -22,10 +22,4 @@ export default class Api {
             params
         })
     }
-
-    getRevenueSummary(companyId, params) {
-        return this.axios().get(`/${companyId}/revenue-summary`, {
-            params
-        })
-    }
 }
diff --git a/resources/js/vue/components/Companies/components/CompanyProfileComponents/CompanySearchAutocomplete.vue b/resources/js/vue/components/Companies/components/CompanyProfileComponents/CompanySearchAutocomplete.vue
new file mode 100644
index 0000000000..dab2d8c3e7
--- /dev/null
+++ b/resources/js/vue/components/Companies/components/CompanyProfileComponents/CompanySearchAutocomplete.vue
@@ -0,0 +1,306 @@
+<template>
+    <div :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
+        <div class="p-5">
+            <div class="flex justify-between items-center mb-4">
+                <h6 class="text-lg font-semibold" :class="[darkMode ? 'text-slate-200' : 'text-slate-800']">
+                    Company Relationships
+                </h6>
+                <button
+                    v-if="!showAddForm"
+                    @click="showAddForm = true"
+                    class="text-primary-500 text-sm font-semibold rounded-md px-3 py-2"
+                    :class="[darkMode ? 'bg-dark-background hover:bg-slate-700' : 'bg-primary-50 hover:bg-primary-100']">
+                    + Add Relationship
+                </button>
+            </div>
+
+            <!-- Add Relationship Form -->
+            <div v-if="showAddForm" class="mb-6 p-4 rounded-lg border" :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border']">
+                <div class="grid grid-cols-1 gap-4">
+                    <div>
+                        <label class="block text-sm font-medium mb-2" :class="[darkMode ? 'text-slate-200' : 'text-slate-700']">
+                            Search Company
+                        </label>
+                        <company-search-autocomplete
+                            v-model="newRelationship.targetCompanyId"
+                            :dark-mode="darkMode"
+                            :search-icon="true"
+                            :enable-loading="true"
+                            @update:modelValue="handleCompanySelection"
+                        />
+                    </div>
+                    <div>
+                        <label class="block text-sm font-medium mb-2" :class="[darkMode ? 'text-slate-200' : 'text-slate-700']">
+                            Relationship Type
+                        </label>
+                        <input
+                            v-model="newRelationship.relationship"
+                            type="text"
+                            placeholder="e.g., Partner, Subsidiary, Competitor"
+                            class="w-full px-3 py-2 border rounded-md text-sm"
+                            :class="[darkMode ? 'bg-dark-background border-dark-border text-slate-200 placeholder-slate-400' : 'bg-white border-light-border text-slate-900 placeholder-slate-500']"
+                        />
+                    </div>
+                    <div class="flex gap-2">
+                        <button
+                            @click="saveRelationship"
+                            :disabled="!canSave || saving"
+                            class="px-4 py-2 text-sm font-medium rounded-md text-white"
+                            :class="[
+                                canSave && !saving 
+                                    ? 'bg-primary-500 hover:bg-primary-600' 
+                                    : 'bg-slate-400 cursor-not-allowed'
+                            ]">
+                            {{ saving ? 'Saving...' : 'Save' }}
+                        </button>
+                        <button
+                            @click="cancelAdd"
+                            class="px-4 py-2 text-sm font-medium rounded-md"
+                            :class="[darkMode ? 'bg-slate-600 hover:bg-slate-700 text-slate-200' : 'bg-slate-200 hover:bg-slate-300 text-slate-700']">
+                            Cancel
+                        </button>
+                    </div>
+                </div>
+            </div>
+
+            <!-- Loading State -->
+            <div v-if="loading" class="flex justify-center py-8">
+                <loading-spinner></loading-spinner>
+            </div>
+
+            <!-- Relationships List -->
+            <div v-else-if="relationships.length > 0" class="space-y-3">
+                <div
+                    v-for="relationship in relationships"
+                    :key="relationship.id"
+                    class="flex items-center justify-between p-4 rounded-lg border"
+                    :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border']">
+                    <div class="flex-1">
+                        <div class="flex items-center gap-3">
+                            <div>
+                                <p class="font-medium" :class="[darkMode ? 'text-slate-200' : 'text-slate-800']">
+                                    {{ relationship.target_company_name }}
+                                </p>
+                                <p class="text-sm" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']">
+                                    {{ relationship.relationship }}
+                                </p>
+                            </div>
+                        </div>
+                        <div class="mt-2 text-xs" :class="[darkMode ? 'text-slate-500' : 'text-slate-500']">
+                            Created by {{ relationship.created_by_name }} on {{ formatDate(relationship.created_at) }}
+                        </div>
+                    </div>
+                    <div class="flex items-center gap-2">
+                        <button
+                            @click="editRelationship(relationship)"
+                            class="p-2 rounded-md"
+                            :class="[darkMode ? 'hover:bg-slate-700 text-slate-400 hover:text-slate-200' : 'hover:bg-slate-100 text-slate-500 hover:text-slate-700']">
+                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
+                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
+                            </svg>
+                        </button>
+                        <button
+                            @click="deleteRelationship(relationship.id)"
+                            class="p-2 rounded-md"
+                            :class="[darkMode ? 'hover:bg-red-900 text-red-400 hover:text-red-300' : 'hover:bg-red-100 text-red-500 hover:text-red-700']">
+                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
+                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
+                            </svg>
+                        </button>
+                    </div>
+                </div>
+            </div>
+
+            <!-- Empty State -->
+            <div v-else class="text-center py-8">
+                <p class="text-sm" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']">
+                    No company relationships found.
+                </p>
+            </div>
+        </div>
+
+        <!-- Edit Modal -->
+        <Modal
+            v-if="editModal"
+            :small="true"
+            @confirm="updateRelationship"
+            @close="closeEditModal"
+            :dark-mode="darkMode"
+            :close-text="'Cancel'"
+            :confirm-text="updating ? 'Updating...' : 'Update'"
+        >
+            <template v-slot:header>
+                <p class="font-medium">Edit Relationship</p>
+            </template>
+            <template v-slot:content>
+                <div class="grid grid-cols-1 gap-3">
+                    <div>
+                        <label class="block text-sm font-medium mb-2" :class="[darkMode ? 'text-slate-200' : 'text-slate-700']">
+                            Company
+                        </label>
+                        <p class="text-sm" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']">
+                            {{ editingRelationship.target_company_name }}
+                        </p>
+                    </div>
+                    <div>
+                        <label class="block text-sm font-medium mb-2" :class="[darkMode ? 'text-slate-200' : 'text-slate-700']">
+                            Relationship Type
+                        </label>
+                        <input
+                            v-model="editingRelationship.relationship"
+                            type="text"
+                            class="w-full px-3 py-2 border rounded-md text-sm"
+                            :class="[darkMode ? 'bg-dark-background border-dark-border text-slate-200' : 'bg-white border-light-border text-slate-900']"
+                        />
+                    </div>
+                </div>
+            </template>
+        </Modal>
+    </div>
+</template>
+
+<script>
+import CompanySearchAutocomplete from "../../../Shared/components/Company/CompanySearchAutocomplete.vue";
+import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
+import Modal from "../../../Shared/components/Modal.vue";
+import SharedApiService from "../../../Shared/services/api";
+
+export default {
+    name: "CompanySearchAutocomplete",
+    components: {
+        CompanySearchAutocomplete,
+        LoadingSpinner,
+        Modal
+    },
+    props: {
+        companyId: {
+            type: Number,
+            required: true
+        },
+        darkMode: {
+            type: Boolean,
+            default: false
+        }
+    },
+    data() {
+        return {
+            api: SharedApiService.make(),
+            loading: false,
+            saving: false,
+            updating: false,
+            showAddForm: false,
+            editModal: false,
+            relationships: [],
+            newRelationship: {
+                targetCompanyId: null,
+                relationship: ''
+            },
+            editingRelationship: {
+                id: null,
+                target_company_name: '',
+                relationship: ''
+            }
+        }
+    },
+    computed: {
+        canSave() {
+            return this.newRelationship.targetCompanyId && this.newRelationship.relationship.trim();
+        }
+    },
+    created() {
+        this.loadRelationships();
+    },
+    methods: {
+        async loadRelationships() {
+            this.loading = true;
+            try {
+                const response = await this.api.axios().get('/company-relationships', {
+                    params: { company_id: this.companyId }
+                });
+                this.relationships = response.data.data || [];
+            } catch (error) {
+                console.error('Error loading relationships:', error);
+                this.relationships = [];
+            } finally {
+                this.loading = false;
+            }
+        },
+        handleCompanySelection(companyId) {
+            this.newRelationship.targetCompanyId = companyId;
+        },
+        async saveRelationship() {
+            if (!this.canSave) return;
+            
+            this.saving = true;
+            try {
+                await this.api.axios().post('/company-relationships', {
+                    company_id: this.companyId,
+                    target_company_id: this.newRelationship.targetCompanyId,
+                    relationship: this.newRelationship.relationship
+                });
+                
+                this.cancelAdd();
+                await this.loadRelationships();
+                this.$emit('relationship-created');
+            } catch (error) {
+                console.error('Error saving relationship:', error);
+                // Handle error (show notification, etc.)
+            } finally {
+                this.saving = false;
+            }
+        },
+        cancelAdd() {
+            this.showAddForm = false;
+            this.newRelationship = {
+                targetCompanyId: null,
+                relationship: ''
+            };
+        },
+        editRelationship(relationship) {
+            this.editingRelationship = { ...relationship };
+            this.editModal = true;
+        },
+        async updateRelationship() {
+            this.updating = true;
+            try {
+                await this.api.axios().post(`/company-relationships/${this.editingRelationship.id}`, {
+                    relationship: this.editingRelationship.relationship
+                });
+                
+                this.closeEditModal();
+                await this.loadRelationships();
+                this.$emit('relationship-updated');
+            } catch (error) {
+                console.error('Error updating relationship:', error);
+                // Handle error
+            } finally {
+                this.updating = false;
+            }
+        },
+        closeEditModal() {
+            this.editModal = false;
+            this.editingRelationship = {
+                id: null,
+                target_company_name: '',
+                relationship: ''
+            };
+        },
+        async deleteRelationship(relationshipId) {
+            if (!confirm('Are you sure you want to delete this relationship?')) return;
+            
+            try {
+                await this.api.axios().delete(`/company-relationships/${relationshipId}`);
+                await this.loadRelationships();
+                this.$emit('relationship-deleted');
+            } catch (error) {
+                console.error('Error deleting relationship:', error);
+                // Handle error
+            }
+        },
+        formatDate(dateString) {
+            return new Date(dateString).toLocaleDateString();
+        }
+    }
+}
+</script>
+</template>
diff --git a/resources/js/vue/components/Companies/components/Revenue.vue b/resources/js/vue/components/Companies/components/Revenue.vue
index 9f27cbd56b..caa6f4f7aa 100644
--- a/resources/js/vue/components/Companies/components/Revenue.vue
+++ b/resources/js/vue/components/Companies/components/Revenue.vue
@@ -1,6 +1,33 @@
 <template>
     <div class="border rounded-lg overflow-hidden"
-         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
+         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
+    >
+        <div class="p-2 flex items-center gap-1">
+            <labeled-value :dark-mode="darkMode" class="flex-1">
+                <template #label>
+                    <div class="flex items-center gap-1">
+                        <p :class="[darkMode ? 'text-white' : 'text-black']"
+                        >
+                            View by
+                        </p>
+                        <p
+                            v-if="revenueOverview.requested_at"
+                            class="text-xs"
+                            :class="[darkMode ? 'text-white' : 'text-black']"
+                        >
+                            (Last update: <time-ago :date="revenueOverview.requested_at" :dark-mode="darkMode" vertical-align="bottom"/>)
+                        </p>
+                    </div>
+                </template>
+                <template #default>
+                    <Dropdown
+                        v-model="viewBy"
+                        :dark-mode="darkMode"
+                        :options="viewByOptions"
+                    />
+                </template>
+            </labeled-value>
+        </div>
         <div class="flex">
             <div
                 class="flex-1 border-l border-b grid grid-cols-4 divide-x"
@@ -18,18 +45,24 @@
                         {{ card.subtitle }}
                     </p>
                     <p v-if="card.dateRangeKey && !loadingSummary" class="text-xs pb-2">
-                        {{ revenueOverview?.[card.dateRangeKey] }} - Now
+                        {{ revenueOverview?.[card.dateRangeKey] ?? card.dateRangeKey }} - Now
                     </p>
                     <div v-if="loadingSummary" class="mt-2">
-                        <div class="h-6 w-24 bg-gray-200 rounded animate-pulse"></div>
+                        <div class="h-6 w-24 rounded animate-pulse" :class="[
+                            darkMode ? 'bg-dark-module' : 'bg-gray-200'
+                        ]"></div>
                     </div>
-                    <p
+                    <div
                         v-else
-                        class="text-lg font-semibold"
-                        :class="card.colorClass"
+                        class="flex items-center gap-1"
                     >
-                        {{ $filters.currency(revenueOverview[card.valueKey]) }}
-                    </p>
+                        <p :class="card.colorClass" class="text-lg font-semibold">
+                            {{ $filters.currency(revenueOverview[card.valueKey]) }}
+                        </p>
+                        <p class="text-xs">
+                            ({{ revenueOverview[card.countKey] }})
+                        </p>
+                    </div>
 
                 </div>
             </div>
@@ -86,6 +119,9 @@ import Api from "../../Billing/services/company-revenue.js";
 import LineChart from "../../Shared/components/LineChart.vue";
 import SimpleCard from "../../MarketingCampaign/SimpleCard.vue";
 import Dropdown from "../../Shared/components/Dropdown.vue";
+import LabeledValue from "../../Shared/components/LabeledValue.vue";
+import CustomCheckbox from "../../Shared/SlideWizard/components/CustomCheckbox.vue";
+import TimeAgo from "../../Shared/components/TimeAgo.vue";
 
 
 function generateGraphData() {
@@ -165,7 +201,7 @@ const DATA_PLACEHOLDER = generateGraphData();
 
 export default {
     name: "Revenue",
-    components: {Dropdown, SimpleCard, LineChart, BarChart, LoadingSpinner, Tab},
+    components: {TimeAgo, CustomCheckbox, LabeledValue, Dropdown, SimpleCard, LineChart, BarChart, LoadingSpinner, Tab},
     props: {
         darkMode: {
             type: Boolean,
@@ -180,14 +216,25 @@ export default {
         return {
             industryId: 'all',
             industryOptions: [],
-            api: ApiService.make(),
             revenueOverview: {},
             loading: false,
+            viewBy: 'billable',
+            viewByOptions: [
+                {
+                    id: 'paid_leads',
+                    name: 'Paid Leads',
+                },
+                {
+                    id: 'billable',
+                    name: 'Chargeable and Delivered',
+                }
+            ],
             title: 'Revenue',
             labels: null,
             statistics: null,
             period: "monthly",
             duration: 1,
+            api: ApiService.make(),
             revenueApiV2: Api.make(),
             loadingSummary: false,
             options: [
@@ -199,33 +246,6 @@ export default {
                 {label: '5 Y', period: 'monthly', duration: 5 * 12},
                 {label: 'All Time', period: 'all-time', duration: 1},
             ],
-            revenueCards: [
-                {
-                    title: 'Revenue All-time',
-                    subtitle: '(Paid Invoices)',
-                    dateRange: null,
-                    valueKey: 'paid_all_time',
-                    colorClass: 'text-green-500',
-                },
-                {
-                    title: 'Last 30 Days',
-                    subtitle: '(Chargeable and Delivered)',
-                    dateRangeKey: 'last_30_days_start_date',
-                    valueKey: 'last_30_days',
-                },
-                {
-                    title: 'Last 6 Months',
-                    subtitle: '(Paid Invoices)',
-                    dateRangeKey: 'last_6_months_start_date',
-                    valueKey: 'last_6_months',
-                },
-                {
-                    title: 'Year to Date',
-                    subtitle: '(Paid Invoices)',
-                    dateRangeKey: 'start_of_year_start_date',
-                    valueKey: 'year_to_date',
-                }
-            ],
             graphData: {
                 data: DATA_PLACEHOLDER,
                 industry_options: []
@@ -261,7 +281,7 @@ export default {
         async getRevenueSummary() {
             this.loadingSummary = true
             try {
-                const data = await this.revenueApiV2.getRevenueSummary(this.companyId)
+                const data = await this.api.getRevenueOverview(this.companyId)
 
                 this.revenueOverview = data.data.data
             } catch (err) {
@@ -280,6 +300,71 @@ export default {
 
             this.getRevenueGraphData();
         },
+    },
+    computed: {
+        revenueCards() {
+            return this.viewBy ==='paid_leads' ? [
+                {
+                    title: 'Revenue All-time',
+                    subtitle: '(Paid Leads)',
+                    dateRangeKey: 'First Delivery',
+                    valueKey: 'total_paid_all_time',
+                    countKey: 'count_paid_all_time',
+                    colorClass: 'text-green-500',
+                },
+                {
+                    title: 'Last 30 Days',
+                    subtitle: '(Paid Leads)',
+                    dateRangeKey: 'last_30_days_start_date',
+                    valueKey: 'paid_last_30_days',
+                    countKey: 'count_last_30_days',
+                },
+                {
+                    title: 'Last 6 Months',
+                    subtitle: '(Paid Leads)',
+                    dateRangeKey: 'last_6_months_start_date',
+                    valueKey: 'total_paid_last_6_months',
+                    countKey: 'count_paid_last_6_months',
+                },
+                {
+                    title: 'Year to Date',
+                    subtitle: '(Paid Leads)',
+                    dateRangeKey: 'start_of_year_start_date',
+                    valueKey: 'total_paid_year_to_date',
+                    countKey: 'count_paid_year_to_date',
+                }
+            ] : [
+                {
+                    title: 'Revenue All-time',
+                    subtitle: '(Chargeable and Delivered)',
+                    dateRangeKey: 'First Delivery',
+                    valueKey: 'total_billable_all_time',
+                    countKey: 'count_billable_all_time',
+                    colorClass: 'text-green-500',
+                },
+                {
+                    title: 'Last 30 Days',
+                    subtitle: '(Chargeable and Delivered)',
+                    dateRangeKey: 'last_30_days_start_date',
+                    valueKey: 'total_billable_last_30_days',
+                    countKey: 'count_billable_last_30_days',
+                },
+                {
+                    title: 'Last 6 Months',
+                    subtitle: '(Chargeable and Delivered)',
+                    dateRangeKey: 'last_6_months_start_date',
+                    valueKey: 'total_billable_last_6_months',
+                    countKey: 'count_billable_last_6_months',
+                },
+                {
+                    title: 'Year to Date',
+                    subtitle: '(Chargeable and Delivered)',
+                    dateRangeKey: 'start_of_year_start_date',
+                    valueKey: 'total_billable_year_to_date',
+                    countKey: 'count_billable_year_to_date',
+                }
+            ]
+        }
     }
 }
 </script>
diff --git a/resources/js/vue/components/Shared/components/LabeledValue.vue b/resources/js/vue/components/Shared/components/LabeledValue.vue
index 7447b8ae50..895c123b35 100644
--- a/resources/js/vue/components/Shared/components/LabeledValue.vue
+++ b/resources/js/vue/components/Shared/components/LabeledValue.vue
@@ -2,7 +2,7 @@
     <div class="flex gap-1 text-sm" :class="[orientationStyle]">
         <div v-if="label || $slots.label" class="flex gap-2">
             <slot name="label">
-                <p class="font-semibold">{{ label }}</p>
+                <p class="font-semibold" :class="[darkMode ? 'text-white' : 'text-black']">{{ label }}</p>
                 <simple-icon
                     v-if="tooltip"
                     :icon="simpleIcon.icons.INFORMATION_CIRCLE"
diff --git a/resources/js/vue/components/Shared/components/TimeAgo.vue b/resources/js/vue/components/Shared/components/TimeAgo.vue
new file mode 100644
index 0000000000..cae9d5943d
--- /dev/null
+++ b/resources/js/vue/components/Shared/components/TimeAgo.vue
@@ -0,0 +1,122 @@
+<template>
+  <span
+      class="relative border-b border-dotted border-gray-400 hover:text-blue-600 transition-colors duration-200"
+      :class="[
+          darkMode ? 'text-white' : 'text-black'
+      ]"
+      @mouseover="showTooltip = true"
+      @mouseleave="showTooltip = false"
+  >
+    {{ timeAgoFormatted }}
+    <div v-if="showTooltip"
+         class="absolute left-full transform -translate-x-1/2 bg-gray-700 text-white px-3 py-2 rounded text-sm whitespace-nowrap z-50 mb-1 shadow-lg"
+         :class="positionClass"
+    >
+      {{ fullDateFormatted }}
+    </div>
+  </span>
+</template>
+
+<script>
+import {DateTime} from 'luxon'
+
+export default {
+    name: 'TimeAgo',
+    props: {
+        darkMode: {
+            type: Boolean,
+            default: false
+        },
+        verticalAlign: {
+            type: String,
+            default: 'top'
+        },
+        date: {
+            type: String,
+            required: true,
+            validator(value) {
+                return DateTime.fromISO(value).isValid
+            }
+        },
+        timezone: {
+            type: String,
+            default: 'America/Denver'
+        },
+        updateInterval: {
+            type: Number,
+            default: 60000
+        }
+    },
+    data() {
+        return {
+            showTooltip: false,
+            now: DateTime.now(),
+            intervalId: null
+        }
+    },
+    computed: {
+        positionClass() {
+            const options = {
+                top: 'bottom-full',
+                bottom: 'top-full'
+            }
+
+            return options[this.verticalAlign] ?? options.top
+        },
+        parsedDate() {
+            return DateTime.fromISO(this.date).setZone(this.timezone)
+        },
+        timeAgoFormatted() {
+            const diff = this.now.diff(this.parsedDate)
+            const duration = diff.shiftTo('years', 'months', 'days', 'hours', 'minutes', 'seconds')
+
+            if (duration.years >= 1) {
+                const years = Math.floor(duration.years)
+                return `${years} year${years !== 1 ? 's' : ''} ago`
+            }
+
+            if (duration.months >= 1) {
+                const months = Math.floor(duration.months)
+                return `${months} month${months !== 1 ? 's' : ''} ago`
+            }
+
+            if (duration.days >= 1) {
+                const days = Math.floor(duration.days)
+                return `${days} day${days !== 1 ? 's' : ''} ago`
+            }
+
+            if (duration.hours >= 1) {
+                const hours = Math.floor(duration.hours)
+                return `${hours} hour${hours !== 1 ? 's' : ''} ago`
+            }
+
+            if (duration.minutes >= 1) {
+                const minutes = Math.floor(duration.minutes)
+                return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`
+            }
+
+            return 'just now'
+        },
+        fullDateFormatted() {
+            return this.parsedDate.toFormat('EEEE, MMMM dd, yyyy \'at\' h:mm:ss a ZZZZ')
+        }
+    },
+    mounted() {
+        // Update the current time periodically to keep relative time accurate
+        this.intervalId = setInterval(() => {
+            this.now = DateTime.now()
+        }, this.updateInterval)
+    },
+    beforeDestroy() {
+        if (this.intervalId) {
+            clearInterval(this.intervalId)
+        }
+    },
+    watch: {
+        timezone() {
+            // Force reactivity when timezone changes
+            this.now = DateTime.now()
+        }
+    }
+}
+</script>
diff --git a/resources/js/vue/components/Shared/modules/RevenueOverview.vue b/resources/js/vue/components/Shared/modules/RevenueOverview.vue
index d1288d17d7..9cf48c879c 100644
--- a/resources/js/vue/components/Shared/modules/RevenueOverview.vue
+++ b/resources/js/vue/components/Shared/modules/RevenueOverview.vue
@@ -3,22 +3,19 @@
         <div class="p-5">
             <div class="flex items-center justify-between pb-4">
                 <h5 class="text-sm uppercase text-primary-500 font-bold leading-tight">Revenue Overview</h5>
+                <p
+                    v-if="revenueOverview.requested_at"
+                    class="text-sm"
+                    :class="[darkMode ? 'text-white' : 'text-black']"
+                >
+                    Last update: <time-ago :date="revenueOverview.requested_at" :dark-mode="darkMode"/>
+                </p>
             </div>
             <div class="grid grid-cols-1 md:grid-cols-2 gap-4" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" v-if="!loading">
                 <div @click="sendToTab('Overview')" class="md:col-span-2 rounded-md p-4 relative cursor-default" :class="[darkMode ? 'bg-dark-background' : 'bg-light-background']">
-                    <p class="text-sm font-medium pb-2" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']">
-                        Outstanding Invoice
-                    </p>
-                    <div class="flex gap-2 items-center">
-                        <p class="text-base font-semibold">
-                            {{ $filters.currency(revenueOverview.v1.outstanding_invoice) }}
-                        </p>
-                        <version-badge v-if="v2" :dark-mode="darkMode" version="v1"/>
-                        <p v-if="v2" class="text-base font-semibold">
-                            {{ $filters.centsToFormattedDollars(revenueOverview.v2.unpaid) }}
-                        </p>
-                        <version-badge v-if="v2" :dark-mode="darkMode"/>
-                    </div>
+                    <labeled-value label="Outstanding to be invoiced" :dark-mode="darkMode">
+                        {{ $filters.currency(revenueOverview.total_to_invoice) }}
+                    </labeled-value>
                 </div>
                 <div @click="sendToTab('Leads')" class="rounded-md p-4 relative cursor-pointer" :class="[darkMode ? 'bg-dark-background' : 'bg-light-background']">
                     <div class="absolute right-0 top-0 mt-2 mr-2">
@@ -26,13 +23,13 @@
                             <path fill-rule="evenodd" clip-rule="evenodd" d="M5.52273 0.818182C5.29679 0.818182 5.11364 0.635026 5.11364 0.409091C5.11364 0.183156 5.29679 0 5.52273 0H8.59084C8.59086 0 8.59089 0 8.59091 0C8.64638 0 8.69927 0.0110395 8.7475 0.0310412C8.79575 0.0510047 8.84096 0.0805976 8.88018 0.11982C8.9194 0.159042 8.949 0.204248 8.96896 0.252498C8.98711 0.296373 8.9973 0.342765 8.99953 0.389467C8.9998 0.395187 8.99996 0.400912 8.99999 0.406637C9 0.407455 9 0.408273 9 0.409091C9 0.409224 9 0.409358 9 0.409492V3.47727C9 3.70321 8.81684 3.88636 8.59091 3.88636C8.36497 3.88636 8.18182 3.70321 8.18182 3.47727V1.39672L6.10127 3.47727L7.85745 5.23346C7.93417 5.31018 7.97727 5.41423 7.97727 5.52273V7.56818C7.97727 7.94792 7.82642 8.31211 7.5579 8.58063C7.28938 8.84915 6.9252 9 6.54545 9H1.43182C1.05208 9 0.687888 8.84915 0.41937 8.58063C0.150852 8.31211 0 7.94792 0 7.56818V2.45455C0 2.0748 0.150852 1.71062 0.41937 1.4421C0.687888 1.17358 1.05208 1.02273 1.43182 1.02273H3.47727C3.58577 1.02273 3.68982 1.06583 3.76654 1.14255L5.52273 2.89873L7.60328 0.818182H5.52273ZM4.94419 3.47727L3.30782 1.84091H1.43182C1.26907 1.84091 1.11299 1.90556 0.997912 2.02064C0.882833 2.13572 0.818182 2.2918 0.818182 2.45455V7.56818C0.818182 7.73093 0.882832 7.88701 0.997912 8.00209C1.11299 8.11717 1.26907 8.18182 1.43182 8.18182H6.54545C6.7082 8.18182 6.86428 8.11717 6.97936 8.00209C7.09444 7.88701 7.15909 7.73093 7.15909 7.56818V5.69218L5.52273 4.05581L3.76654 5.812C3.60678 5.97176 3.34776 5.97176 3.188 5.812C3.02824 5.65224 3.02824 5.39322 3.188 5.23346L4.94419 3.47727Z"/>
                         </svg>
                     </div>
-                    <p class="text-sm font-medium pb-2" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']">
-                        Last Lead
-                    </p>
-                    <p class="text-base font-semibold">
-                        {{ revenueOverview.v1.last_lead ? $filters.dateFromTimestamp(this.revenueOverview.v1.last_lead)
-                         : "No lead purchased yet" }}
-                    </p>
+                    <labeled-value label="Last Delivery at" :dark-mode="darkMode">
+                        {{
+                            revenueOverview.last_delivered_at
+                                ? $filters.dateFromTimestamp(revenueOverview.last_delivered_at)
+                                : "No lead purchased yet"
+                        }}
+                    </labeled-value>
                 </div>
                 <div @click="sendToTab('Revenue')" class="rounded-md p-4 relative cursor-pointer" :class="[darkMode ? 'bg-dark-background' : 'bg-light-background']">
                     <div class="absolute right-0 top-0 mt-2 mr-2">
@@ -40,40 +37,26 @@
                             <path fill-rule="evenodd" clip-rule="evenodd" d="M5.52273 0.818182C5.29679 0.818182 5.11364 0.635026 5.11364 0.409091C5.11364 0.183156 5.29679 0 5.52273 0H8.59084C8.59086 0 8.59089 0 8.59091 0C8.64638 0 8.69927 0.0110395 8.7475 0.0310412C8.79575 0.0510047 8.84096 0.0805976 8.88018 0.11982C8.9194 0.159042 8.949 0.204248 8.96896 0.252498C8.98711 0.296373 8.9973 0.342765 8.99953 0.389467C8.9998 0.395187 8.99996 0.400912 8.99999 0.406637C9 0.407455 9 0.408273 9 0.409091C9 0.409224 9 0.409358 9 0.409492V3.47727C9 3.70321 8.81684 3.88636 8.59091 3.88636C8.36497 3.88636 8.18182 3.70321 8.18182 3.47727V1.39672L6.10127 3.47727L7.85745 5.23346C7.93417 5.31018 7.97727 5.41423 7.97727 5.52273V7.56818C7.97727 7.94792 7.82642 8.31211 7.5579 8.58063C7.28938 8.84915 6.9252 9 6.54545 9H1.43182C1.05208 9 0.687888 8.84915 0.41937 8.58063C0.150852 8.31211 0 7.94792 0 7.56818V2.45455C0 2.0748 0.150852 1.71062 0.41937 1.4421C0.687888 1.17358 1.05208 1.02273 1.43182 1.02273H3.47727C3.58577 1.02273 3.68982 1.06583 3.76654 1.14255L5.52273 2.89873L7.60328 0.818182H5.52273ZM4.94419 3.47727L3.30782 1.84091H1.43182C1.26907 1.84091 1.11299 1.90556 0.997912 2.02064C0.882833 2.13572 0.818182 2.2918 0.818182 2.45455V7.56818C0.818182 7.73093 0.882832 7.88701 0.997912 8.00209C1.11299 8.11717 1.26907 8.18182 1.43182 8.18182H6.54545C6.7082 8.18182 6.86428 8.11717 6.97936 8.00209C7.09444 7.88701 7.15909 7.73093 7.15909 7.56818V5.69218L5.52273 4.05581L3.76654 5.812C3.60678 5.97176 3.34776 5.97176 3.188 5.812C3.02824 5.65224 3.02824 5.39322 3.188 5.23346L4.94419 3.47727Z"/>
                         </svg>
                     </div>
-                    <p class="text-sm font-medium pb-2" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']">
-                        Revenue All-time
-                    </p>
-                    <div class="flex gap-2 items-center">
-                        <p class="text-base font-semibold">
-                            {{ $filters.currency(revenueOverview.v1.revenue_all_time) }}
-                        </p>
-                        <version-badge v-if="v2" :dark-mode="darkMode" version="v1"/>
-                        <p v-if="v2" class="text-base font-semibold">
-                            {{ $filters.centsToFormattedDollars(revenueOverview.v2.paid) }}
-                        </p>
-                        <version-badge v-if="v2" :dark-mode="darkMode"/>
-                    </div>
-
+                    <labeled-value label="Revenue All-Time (Chargeable and Delivered Leads)" :dark-mode="darkMode">
+                        <div class="flex items-center gap-1">
+                            <p>{{ $filters.currency(revenueOverview.total_billable_all_time) }}</p>
+                            <p class="text-xs">({{revenueOverview.count_billable_all_time}})</p>
+                        </div>
+                    </labeled-value>
                 </div>
 
                 <div class="rounded-md p-4 relative cursor-pointer" :class="[darkMode ? 'bg-dark-background' : 'bg-light-background']">
-                    <p class="text-sm font-medium pb-2" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']">
-                        Average Daily Spend
-                    </p>
-                    <p class="text-base font-semibold">
-                        {{ averageDailySpendLast30Days.format() }}
-                    </p>
+                    <labeled-value label="30-Day Daily Spend Average (Chargeable and Delivered Leads)" :dark-mode="darkMode">
+                        <p>{{ $filters.currency(revenueOverview.average_daily_spend_last_30_days) }}</p>
+                    </labeled-value>
                 </div>
                 <div class="rounded-md p-4 relative cursor-pointer" :class="[darkMode ? 'bg-dark-background' : 'bg-light-background']">
-                    <p class="text-sm font-medium pb-2" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']">
-                        Leads In Last 30 Days
-                    </p>
-                    <div class="flex gap-2 items-center">
-                        <p class="text-base font-semibold">
-                            {{ revenueOverview.v1.count_of_leads_last_30_days }}
-                        </p>
-                    </div>
-
+                    <labeled-value label="Leads In Last 30 Days (Chargeable and Delivered Leads)" :dark-mode="darkMode">
+                        <div class="flex items-center gap-1">
+                            <p>{{ $filters.currency(revenueOverview.total_billable_last_30_days) }}</p>
+                            <p class="text-xs">({{revenueOverview.count_billable_last_30_days}})</p>
+                        </div>
+                    </labeled-value>
                 </div>
             </div>
             <div v-if="loading">
@@ -90,9 +73,11 @@ import ApiService from "../../Companies/services/api";
 import LoadingSpinner from "../components/LoadingSpinner.vue";
 import VersionBadge from "../../Billing/VersionBadge.vue";
 import currency from "currency.js";
+import LabeledValue from "../components/LabeledValue.vue";
+import TimeAgo from "../components/TimeAgo.vue";
 export default {
     name: "RevenueOverview",
-    components: {VersionBadge, Tab, LoadingSpinner},
+    components: {TimeAgo, LabeledValue, VersionBadge, Tab, LoadingSpinner},
     props: {
         darkMode: {
             type: Boolean,
@@ -106,14 +91,7 @@ export default {
     data() {
         return {
             api: ApiService.make(),
-            revenueOverview: {
-                v1: {
-                    outstanding_invoice: null,
-                    last_lead: null,
-                    revenue_all_time: null
-                },
-                v2: null
-            },
+            revenueOverview: {},
             loading: false
         }
     },
@@ -121,11 +99,8 @@ export default {
         if (this.companyId) this.getRevenueOverview();
     },
     computed: {
-        v2() {
-            return this.revenueOverview?.v2;
-        },
         averageDailySpendLast30Days() {
-            return currency(this.revenueOverview.v1.average_daily_spend_last_30_days);
+            return currency(this.revenueOverview.average_daily_spend_last_30_days);
         }
     },
     methods: {
diff --git a/routes/internal-api-billing.php b/routes/internal-api-billing.php
index fb0b33f190..f999b4e11f 100644
--- a/routes/internal-api-billing.php
+++ b/routes/internal-api-billing.php
@@ -184,4 +184,3 @@
 
 
 Route::get('/v2/companies/{company}/revenue-graph', [CompanyRevenueController::class, 'getRevenueGraphData']);
-Route::get('/v2/companies/{company}/revenue-summary', [CompanyRevenueController::class, 'getRevenueSummary']);
diff --git a/tests/Feature/Http/Controllers/API/CompaniesControllerTest.php b/tests/Feature/Http/Controllers/API/CompaniesControllerTest.php
index eba465cbbd..6ab44c69e5 100644
--- a/tests/Feature/Http/Controllers/API/CompaniesControllerTest.php
+++ b/tests/Feature/Http/Controllers/API/CompaniesControllerTest.php
@@ -308,26 +308,35 @@ public function it_gets_revenue_overview_for_v2()
             ->assertOk()
             ->assertJsonStructure([
                 'data' => [
-                    'v1' => [
-                        'outstanding_invoice',
-                        'last_lead',
-                        'revenue_all_time',
-                        'chargeable_last_30_days',
-                        'revenue_last_30_days',
-                        'revenue_last_6_months',
-                        'graph_data',
-                        'revenue_year_to_date',
-                        'last_30_days_start_date',
-                        'last_6_month_start_date',
-                        'start_of_year_start_date',
-                        'average_daily_spend_last_30_days',
-                        'count_of_leads_last_30_days',
-                    ],
-                    'v2' => [
-                        'total',
-                        'paid',
-                        'unpaid',
-                    ],
+                    'last_30_days_start_date',
+                    'last_6_months_start_date',
+                    'start_of_year_start_date',
+                    'a2_all_time_paid',
+                    'legacy_all_time_paid',
+                    'total_paid_all_time',
+                    'count_paid_all_time',
+                    'total_paid_last_30_days',
+                    'count_paid_last_30_days',
+                    'total_paid_last_6_months',
+                    'count_paid_last_6_months',
+                    'total_paid_year_to_date',
+                    'count_paid_year_to_date',
+                    'total_billable_all_time',
+                    'count_billable_all_time',
+                    'count_billable_last_30_days',
+                    'total_billable_last_30_days',
+                    'count_billable_last_6_months',
+                    'total_billable_last_6_months',
+                    'count_billable_year_to_date',
+                    'total_billable_year_to_date',
+                    'average_daily_spend_last_30_days',
+                    'total_rejected_all_time',
+                    'count_rejected_all_time',
+                    'total_cancelled_all_time',
+                    'count_cancelled_all_time',
+                    'last_delivered_at',
+                    'total_to_invoice',
+                    'requested_at',
                 ],
             ]);
     }
diff --git a/tests/Feature/Jobs/Companies/ReleaseCompaniesBackToQueueTest.php b/tests/Feature/Jobs/Companies/ReleaseCompaniesBackToQueueTest.php
index 0eb03f8b20..5a7e7ee6a1 100644
--- a/tests/Feature/Jobs/Companies/ReleaseCompaniesBackToQueueTest.php
+++ b/tests/Feature/Jobs/Companies/ReleaseCompaniesBackToQueueTest.php
@@ -57,7 +57,7 @@ public function a_company_is_released_back_to_the_queue_if_no_tasks_or_contact_i
     }
 
     #[Test]
-    public function a_company_is_released_even_if_they_only_have_an_sdr_assigned()
+    public function a_company_is_ignored_if_it_does_not_have_a_bdm_assigned()
     {
         $this->travel(-5)->days();
 
@@ -73,7 +73,7 @@ public function a_company_is_released_even_if_they_only_have_an_sdr_assigned()
 
         $company->refresh();
 
-        $this->assertNull($company->salesDevelopmentRepresentative);
+        $this->assertNotNull($company->salesDevelopmentRepresentative);
         $this->assertNull($company->businessDevelopmentManager);
     }
 
